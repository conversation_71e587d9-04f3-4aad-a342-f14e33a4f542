{"name": "formatfuse", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "check": "astro check", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/partytown": "^2.1.4", "@astrojs/react": "^4.3.0", "@astrojs/sitemap": "^3.4.1", "@jsquash/avif": "^2.1.1", "@jsquash/jpeg": "^1.6.0", "@jsquash/png": "^3.1.1", "@jsquash/resize": "^2.1.0", "@jsquash/webp": "^1.5.0", "@lucide/astro": "^0.525.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@refilelabs/image": "^0.2.5", "@resvg/resvg-js": "^2.6.2", "@resvg/resvg-wasm": "^2.6.2", "@shikijs/langs": "^3.7.0", "@shikijs/themes": "^3.7.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@types/canvas-confetti": "^1.9.0", "@types/js-yaml": "^4.0.9", "astro": "^5.10.1", "astro-opengraph-images": "^1.13.1", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "colorjs.io": "^0.5.2", "comlink": "^4.4.2", "embla-carousel-react": "^8.6.0", "file-saver": "^2.0.5", "framer-motion": "^12.20.1", "js-yaml": "^4.1.0", "jszip": "^3.10.1", "libarchive-wasm": "^1.2.0", "libheif-js": "^1.19.8", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "pako": "^2.1.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.3.31", "qrcode": "^1.5.4", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-window": "^1.8.11", "shiki": "^3.7.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tar-js": "^0.3.0", "uuid": "^11.1.0"}, "packageManager": "pnpm@10.12.4", "devDependencies": {"@astrojs/check": "^0.9.4", "@fontsource/inter": "^5.2.6", "@types/archiver": "^6.0.3", "@types/file-saver": "^2.0.7", "@types/jsdom": "^21.1.7", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/tar": "^6.1.13", "@types/tar-js": "^0.3.5", "@vitest/coverage-v8": "^3.2.4", "archiver": "^7.0.1", "canvas": "^3.1.2", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "react": "^19.1.0", "tar": "^7.4.3", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "tw-to-css": "^0.0.12", "typescript": "^5.8.3", "vitest": "^3.2.4"}}